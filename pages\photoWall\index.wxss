/* pages/photoWall/index.wxss */
.photo-wall-container {
  background-color: #f5f5f5;
  padding-bottom: 160rpx;
}

/* 标签页样式 */
.tab-container {
  display: flex;
  background-color: #fff;
  margin: 20rpx 30rpx;
  border-radius: 12rpx;
  padding: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}

.tab-item.active {
  background-color: #ff438f;
  color: #fff;
  font-weight: bold;
}

/* 照片列表样式 */
.photo-list {
  padding: 0 30rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  opacity: 0.5;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
  margin-top: 20rpx;
}

.photo-grid {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.photo-item {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.photo-item:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.photo-card {
  padding: 30rpx;
}

/* 照片对比样式 */
.photo-comparison {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.before-photo,
.after-photo {
  flex: 1;
  position: relative;
}

.photo-image {
  width: 100%;
  height: 300rpx;
  border-radius: 12rpx;
  background-color: #f0f0f0;
}

.photo-label {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

/* 照片信息样式 */
.photo-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.photo-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
}

.photo-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pet-info {
  font-size: 26rpx;
  color: #666;
}

.service-type {
  font-size: 24rpx;
  color: #ff438f;
  background-color: rgba(255, 67, 143, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.photo-stats {
  display: flex;
  gap: 40rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.icon-heart {
  font-size: 28rpx;
  color: #ccc;
  transition: color 0.3s ease;
}

.icon-heart.liked {
  color: #ff438f;
}

.icon-view {
  font-size: 24rpx;
}

.stat-text {
  font-size: 24rpx;
  color: #666;
}

/* 加载更多样式 */
.load-more {
  text-align: center;
  padding: 40rpx 0;
  color: #666;
  font-size: 28rpx;
}
