import photoWallApi from '../../api/modules/photoWall.js';

Page({
  data: {
    //用户全局信息，page-extend.js会自动注入
    userInfo: null,
    currentTab: 'latest', // 当前标签页：latest 最新, popular 热门
    photoList: [], // 照片列表
    loading: false, // 是否正在加载
    loadingMore: false, // 是否正在加载更多
    hasMore: true, // 是否还有更多数据
    page: 1, // 当前页码
    limit: 10, // 每页数量
  },

  onLoad(options) {
    this.loadPhotoList();
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData();
  },

  onPullDownRefresh() {
    this.refreshData();
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loadingMore) {
      this.loadMore();
    }
  },

  /**
   * 切换标签页
   */
  switchTab(event) {
    const tab = event.currentTarget.dataset.tab;
    if (tab === this.data.currentTab) return;

    this.setData({
      currentTab: tab,
      photoList: [],
      page: 1,
      hasMore: true,
    });
    this.loadPhotoList();
  },

  /**
   * 刷新数据
   */
  refreshData() {
    this.setData({
      photoList: [],
      page: 1,
      hasMore: true,
    });
    this.loadPhotoList();
  },

  /**
   * 加载照片列表
   */
  async loadPhotoList() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const params = {
        limit: this.data.limit,
      };

      let result;
      if (this.data.currentTab === 'latest') {
        result = await photoWallApi.getLatestList(params);
      } else {
        result = await photoWallApi.getPopularList(params);
      }

      if (result && result.list) {
        const newList = result.list.map(item => ({
          ...item,
          isLiked: false, // 初始化点赞状态，实际应该从后端获取
        }));

        this.setData({
          photoList: newList,
          hasMore: newList.length >= this.data.limit,
        });
      }
    } catch (error) {
      console.error('加载照片列表失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none',
      });
    } finally {
      this.setData({ loading: false });
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 加载更多
   */
  async loadMore() {
    if (this.data.loadingMore || !this.data.hasMore) return;

    this.setData({ loadingMore: true });

    try {
      const params = {
        limit: this.data.limit,
        offset: this.data.photoList.length,
      };

      let result;
      if (this.data.currentTab === 'latest') {
        result = await photoWallApi.getLatestList(params);
      } else {
        result = await photoWallApi.getPopularList(params);
      }

      if (result && result.list) {
        const newList = result.list.map(item => ({
          ...item,
          isLiked: false, // 初始化点赞状态
        }));

        this.setData({
          photoList: [...this.data.photoList, ...newList],
          hasMore: newList.length >= this.data.limit,
          page: this.data.page + 1,
        });
      }
    } catch (error) {
      console.error('加载更多失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none',
      });
    } finally {
      this.setData({ loadingMore: false });
    }
  },

  /**
   * 查看照片详情
   */
  viewPhotoDetail(event) {
    const item = event.currentTarget.dataset.item;
    if (!item || !item.id) {
      wx.showToast({
        title: '照片信息错误',
        icon: 'none',
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/photoWall/detail?id=${item.id}`,
      fail: (err) => {
        console.error('页面跳转失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none',
        });
      }
    });
  },
});
