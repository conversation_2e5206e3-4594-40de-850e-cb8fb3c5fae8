import orderApi from '../../api/modules/order.js';
import payApi from '../../api/modules/pay';
import reviewApi from '../../api/modules/review';
import utils from '../utils/util';
import { OrderStatus } from '../../common/constant.js';
import WeMessage from '../../common/WeMessage.js';

Page({
  data: {
    userInfo: null,
    // 订单状态标签
    orderTabs: [
      {
        name: '全部',
        status: 'all',
      },
      {
        name: OrderStatus.待付款,
        status: OrderStatus.待付款,
      },
      {
        name: OrderStatus.待接单,
        status: OrderStatus.待接单,
      },
      {
        name: '进行中',
        status: [OrderStatus.待服务, OrderStatus.已出发, OrderStatus.服务中, OrderStatus.退款中].join(','),
        hasSubFilters: true,
        subFilters: [
          { name: '全部', status: [OrderStatus.待服务, OrderStatus.已出发, OrderStatus.服务中, OrderStatus.退款中].join(',') },
          { name: OrderStatus.待服务, status: OrderStatus.待服务 },
          { name: OrderStatus.已出发, status: OrderStatus.已出发 },
          { name: OrderStatus.服务中, status: OrderStatus.服务中 },
          { name: OrderStatus.退款中, status: OrderStatus.退款中 },
        ]
      },
      {
        name: OrderStatus.已完成,
        status: [OrderStatus.已完成, OrderStatus.已退款, OrderStatus.已评价].join(','),
        hasSubFilters: true,
        subFilters: [
          { name: '全部', status: [OrderStatus.已完成, OrderStatus.已退款, OrderStatus.已评价].join(',') },
          { name: OrderStatus.已完成, status: OrderStatus.已完成 },
          { name: OrderStatus.已评价, status: OrderStatus.已评价 },
          { name: OrderStatus.已退款, status: OrderStatus.已退款 },
        ]
      },
    ],
    currentTab: 'all', // 当前选中的标签
    currentSubFilter: '', // 当前选中的子筛选器
    showSubFilters: false, // 是否显示子筛选器
    orderList: [], // 订单列表
    page: 1, // 当前页码
    pageSize: 10, // 每页数量
    showModal: false,
    modalTitle: '敬请期待',
    modalContent: '支付功能稍后上线！',
    confirmText: '知道了',
    currentSn: '',
    modalLinks: [],
    modalButtons: [],
  },

  /**
   * 判断订单是否可以联系员工
   * 订单被接单后（待服务、已出发、服务中）到订单结束前可以联系员工
   */
  canCallEmployee(orderStatus) {
    const callableStatuses = [
      OrderStatus.待服务,
      OrderStatus.已出发,
      OrderStatus.服务中
    ];

    return callableStatuses.includes(orderStatus);
  },

  /**
   * 联系员工
   */
  callEmployee(e) {
    const { order } = e.currentTarget.dataset;

    if (!this.canCallEmployee(order.status)) {
      wx.showToast({
        title: '当前订单状态不支持联系员工',
        icon: 'none'
      });
      return;
    }

    if (!order.employee || !order.employee.phone) {
      wx.showToast({
        title: '员工联系方式不可用',
        icon: 'none'
      });
      return;
    }

    wx.makePhoneCall({
      phoneNumber: order.employee.phone,
      success: () => {
        console.log('成功发起电话呼叫');
      },
      fail: (err) => {
        console.error('电话呼叫失败', err);
        wx.showToast({
          title: '拨打电话失败',
          icon: 'none'
        });
      }
    });
  },

  onLoad(option) {
    // 获取参数，支持 curTab 和 type 两种参数名以保证兼容性
    const type = option.curTab || option.type;
    if (type) {
      // 找到对应的标签配置
      const currentTabConfig = this.data.orderTabs.find(tab => tab.status === type);

      this.setData({
        currentTab: type,
        showSubFilters: currentTabConfig && currentTabConfig.hasSubFilters,
        currentSubFilter: currentTabConfig && currentTabConfig.hasSubFilters ? currentTabConfig.subFilters[0].status : '',
      });
    }
    // 加载初始订单数据
    this.loadOrders();
  },
  onShow() {
    this.loadOrders();
  },

  onShareAppMessage: function () {
    return {
      title: '贝宠约洗，足不出户享精致洗护～',
      path: `/pages/index/index?aUserId=${this.data?.userInfo?.id || ''}&shareCode=${
        this.data?.userInfo?.promotionCode || ''
      }&shareTime=${Date.now()}`,
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share.png',
      // imageUrl: '/assets/share.png' // 这里填写图片的相对路径或绝对路径
    };
  },
  onShareTimeline: function () {
    return {
      title: '「忙到没空送洗？上门服务解忧！」贝宠约洗，专业到家超省心～',
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share1.png',
      query: `aUserId=${this.data?.userInfo?.id || ''}&shareCode=${
        this.data?.userInfo?.promotionCode || ''
      }&shareTime=${Date.now()}`,
      // imageUrl: '/assets/share.png' // 这里填写图片的相对路径或绝对路径
    };
  },

  // 切换订单状态标签
  switchTab(e) {
    const status = e.currentTarget.dataset.status;
    const hasSubFilters = e.currentTarget.dataset.hasSubFilters;

    // 如果点击的是有子筛选器的标签
    if (hasSubFilters && this.data.currentTab === status) {
      // 如果当前已经选中该标签，则切换子筛选器显示状态
      this.setData({
        showSubFilters: !this.data.showSubFilters
      });
      return;
    }

    // 找到对应的标签配置
    const currentTabConfig = this.data.orderTabs.find(tab => tab.status === status);

    this.setData({
      currentTab: status,
      showSubFilters: hasSubFilters ? true : false,
      currentSubFilter: hasSubFilters ? currentTabConfig.subFilters[0].status : '',
      page: 1, // 重置页码
      orderList: [], // 清空当前列表
    });
    this.loadOrders();
  },

  // 切换子筛选器
  switchSubFilter(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      currentSubFilter: status,
      page: 1, // 重置页码
      orderList: [], // 清空当前列表
    });
    this.loadOrders();
  },

  // 加载订单数据
  async loadOrders() {
    const userInfo = this.data.userInfo;
    let currentTab = this.data.currentTab;

    // 如果有子筛选器且已选择，使用子筛选器的状态
    if (this.data.showSubFilters && this.data.currentSubFilter) {
      currentTab = this.data.currentSubFilter;
    }

    console.log('currentTab----', currentTab);
    if (userInfo) {
      try {
        // 获取订单列表
        const res = await orderApi.getlists(userInfo.id, currentTab);
        const data = await Promise.all(res.map(async item => {
          const orderData = {
            ...item,
            serviceTimeText: item.serviceTime ? utils.formatNormalDate(item.serviceTime) : null,
            createdAt: item.createdAt ? utils.formatNormalDate(item.createdAt) : null,
            hasReview: false, // 默认没有评价
            // 格式化追加服务相关金额
            totalAmount: item.hasAdditionalServices
              ? ((item.totalFee * 1) + (item.additionalServiceAmount * 1)).toFixed(2)
              : (item.totalFee * 1).toFixed(2),
            formattedTotalFee: (item.totalFee * 1).toFixed(2),
            formattedAdditionalAmount: (item.additionalServiceAmount * 1).toFixed(2),
          };

          // 如果订单状态是已完成或已评价，检查是否有评价
          if (item.status === '已完成' || item.status === '已评价') {
            try {
              const review = await reviewApi.getByOrderId(item.id);
              if (review) {
                orderData.hasReview = true;
                orderData.reviewData = review;
              }
            } catch (error) {
              // 如果获取评价失败（比如404），说明没有评价，保持hasReview为false
              console.log(`订单${item.id}暂无评价`);
            }
          }

          return orderData;
        }));

        this.setData({
          orderList: data || [],
        });
      } catch (error) {
        console.error('加载订单失败:', error);
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    }
    wx.hideLoading();
  },

  // 加载更多订单
  loadMoreOrders() {
    this.setData({
      page: this.data.page + 1,
    });
    this.loadOrders();
  },

  // 切换更多操作弹窗
  toggleOrderActions(e) {
    const orderId = e.currentTarget.dataset.orderId;
    const orderList = this.data.orderList.map(order => {
      if (order.orderId === orderId) {
        order.showMoreActions = !order.showMoreActions;
      } else {
        // 关闭其他订单的更多操作
        order.showMoreActions = false;
      }
      return order;
    });

    this.setData({
      orderList,
    });
  },

  // 阻止事件冒泡
  preventTap(e) {
    e.stopPropagation();
  },

  // 查看订单详情
  viewOrderDetail(e) {
    const thiz = this;
    const orderDetail = e.currentTarget.dataset.orderdetail;
    thiz.navigateTo({
      type: 'page',
      url: '/pages/serviceOrder/orderDetail/index',
      actionType: 'check',
      data: orderDetail,
    });
    // wx.navigateTo({
    //   url: `/pages/serviceOrder/orderDetail/index?orderId=${orderId}`,
    // });
  },

  // 取消订单
  cancelOrder(e) {
    const _this = this;
    const orderId = e.currentTarget.dataset.orderId;
    wx.showModal({
      title: '取消订单',
      content: '确定要取消此订单吗？',
      success: res => {
        if (res.confirm) {
          const userInfo = this.data.userInfo;
          orderApi.cancel(userInfo.id, orderId).then(res => {
            if (res) {
              wx.showToast({
                title: '取消成功',
                icon: 'success',
              });
              _this.loadOrders();
            }
          });
        }
      },
    });
  },

  // 支付订单
  payOrder(e) {
    const _this = this;
    const userInfo = this.data.userInfo;
    if (!userInfo) {
      this.navigateTo({
        type: 'tip',
        tip: '获取订单信息失败，请重新登录',
      });
      return;
    }
    const sn = e.currentTarget.dataset.sn;
    const order = this.data.orderList.find(item => item.sn === sn);

    if (!order) {
      wx.showToast({
        title: '订单信息不存在',
        icon: 'none',
      });
      return;
    }

    if (Number(order.amount) === 0) {
      wx.showLoading({
        title: '处理中',
      });
      // 使用统一的0元订单处理方法
      orderApi.handleZeroAmountOrder(userInfo.id, sn).then(res => {
        wx.hideLoading();
        if (res.success) {
          // 显示自定义模态框
          // _this.setData({
          //   modal: {
          //     isShow: false,
          //   },
          //   showModal: true,
          //   modalTitle: '提交成功',
          //   modalContent: '护理师会尽快和您联系，请耐心等待',
          // });
          // 刷新订单列表
          _this.loadOrders();
          console.log('4----', userInfo);
          this.handlePaymentSuccessModal(userInfo.openid, order.id);
        } else {
          wx.showToast({
            title: res.error || '处理失败，请稍后重试',
            icon: 'none',
          });
        }
      });
      return;
    }

    // 支付
    payApi.doPay({
      sn,
      onOk: () => {
        // 支付成功后，调用支付成功接口更新订单状态
        orderApi.pay(userInfo.id, sn).then(res => {
          // 显示自定义模态框
          // _this.setData({
          //   modal: {
          //     isShow: false,
          //   },
          //   showModal: true,
          //   modalTitle: '提交成功',
          //   modalContent: '护理师会尽快和您联系，请耐心等待',
          // });
          _this.loadOrders();
          console.log('5----', userInfo);
          this.handlePaymentSuccessModal(userInfo.openid, order.id);
        });
      },
      onCancel: () => {
        wx.showToast({
          title: '取消支付',
          icon: 'none',
        });
      },
      onError: () => {
        wx.showToast({
          title: '支付失败',
          icon: 'none',
        });
      },
      complete: () => {},
    });
  },
  // 退款（待接单状态）
  refund(e) {
    const _this = this;
    const sn = e.currentTarget.dataset.sn;
    wx.showModal({
      title: '申请退款',
      content: '确定要申请退款吗？',
      confirmText: '确定',
      cancelText: '取消',
      success: res => {
        if (res.confirm) {
          payApi.refund(sn).then(res => {
            console.log('退款申请结果：', res);
            if (res) {
              wx.showToast({
                title: '退款申请成功',
                icon: 'success',
              });
              setTimeout(() => {
                _this.loadOrders();
              }, 1000);
            } else {
              wx.showToast({
                title: '退款申请失败',
                icon: 'none',
              });
            }
          });
        }
      },
    });
  },

  // 申请退款，需要后台审核（已接单状态）
  refundRequest(e) {
    const sn = e.currentTarget.dataset.sn;

    // 准备申请退款
    // 使用custom-modal组件显示提示，并配置链接
    this.setData({
      showModal: true,
      modalTitle: '申请退款',
      modalContent: '由于商户已接单，申请退款可能产生一定费用。具体规则请查看用户协议的订单修改部分。',
      currentSn: sn, // 保存当前订单号，用于后续处理
      // 配置链接
      modalLinks: [
        {
          text: '用户协议',
          url: '/pages/mine/userAgreement/index',
        },
      ],
      // 配置按钮
      modalButtons: [
        {
          text: '取消',
          type: 'cancel',
          event: 'cancel',
        },
        {
          text: '申请退款',
          type: 'primary',
          event: 'confirm',
        },
      ],
    });
  },

  // 模态框确认按钮点击事件
  onModalConfirm() {
    const _this = this;
    const { userInfo, currentSn: sn } = this.data;

    if (sn && userInfo?.id) {
      // 用户确认申请退款
      orderApi.refundRequest(userInfo.id, sn).then(res => {
        console.log('退款申请结果：', res);
        if (res) {
          wx.showToast({
            title: '退款申请成功，请耐心等待处理',
            icon: 'success',
          });
          setTimeout(() => {
            _this.loadOrders();
          }, 1000);
        }
      });

      // 清除当前订单号
      this.setData({
        currentSn: '',
      });
    }
  },

  // 处理链接点击事件
  handleLinkTap(e) {
    const link = e.detail.link;
    console.log('链接点击：', link);

    // 如果需要特殊处理某些链接，可以在这里添加逻辑
  },

  // 处理模态框取消按钮点击事件
  handleModalCancel() {
    console.log('用户取消了退款申请');
    // 清除当前订单号
    this.setData({
      currentSn: '',
    });
  },

  // 催接单
  confirmReceipt(e) {
    const orderId = e.currentTarget.dataset.orderId;
    wx.showModal({
      title: '确认催接单',
      content: '是否向洗护人员发起催接单申请？',
      success: res => {
        if (res.confirm) {
          wx.showToast({
            title: '确认成功',
            icon: 'success',
          });
        }
      },
    });
  },
  // 修改上门时间
  reschedule(e) {},

  // 去评价
  reviewOrder(e) {
    const orderId = e.currentTarget.dataset.orderId;
    wx.navigateTo({
      url: `/pages/orderReview/orderReview?orderId=${orderId}`,
    });
  },

  // 查看评价
  viewReview(e) {
    const orderId = e.currentTarget.dataset.orderId;
    wx.navigateTo({
      url: `/pages/reviewDetail/reviewDetail?orderId=${orderId}`,
    });
  },

  /**
   * 处理支付成功后的弹窗 - 订单列表页面
   * @param {string} openId 用户openId
   * @param {string} sn 订单编号
   */
  handlePaymentSuccessModal(openId, sn) {
    const weMessage = new WeMessage(openId, sn, OrderStatus.待接单);
    const modalConfig = weMessage.handlePaymentSuccess();
    if (modalConfig) {
      this.setData({
        showModal: true,
        modalTitle: modalConfig.modalConfig.title,
        modalContent: modalConfig.modalConfig.content,
        modalButtons: modalConfig.modalConfig.buttons
      });
      this._weMessage = weMessage;
    }
  },

  // 处理订阅确认按钮点击
  handleModalConfirm(e) {
    if (this._weMessage) {
      this._weMessage.requestOrderConfirmationSubscribe();
    }
    this.setData({ showModal: false });
  },

  // 处理订阅取消按钮点击
  handleModalCancel(e) {
    if (this._weMessage) {
      this._weMessage.recordUserChoice(false);
    }
    this.setData({ showModal: false });
  },
});
