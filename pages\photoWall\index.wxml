<view class="container photo-wall-container">
  <diy-navbar :isFixed="true" CustomBar='60' bgCustom="#fff" isBack="true" backUrl="/pages/index/index">
    <view slot="content">
      <view class="flex align-center flex-nowrap justify-center diygw-col-24">
        照片墙
      </view>
    </view>
  </diy-navbar>

  <!-- 标签页切换 -->
  <view class="tab-container">
    <view class="tab-item {{currentTab === 'latest' ? 'active' : ''}}" bind:tap="switchTab" data-tab="latest">
      最新照片
    </view>
    <view class="tab-item {{currentTab === 'popular' ? 'active' : ''}}" bind:tap="switchTab" data-tab="popular">
      热门照片
    </view>
  </view>

  <!-- 照片列表 -->
  <view class="photo-list">
    <view wx:if="{{photoList.length === 0 && !loading}}" class="empty-state">
      <image src="//xian7.zos.ctyun.cn/pet/static/empty.png" class="empty-image" mode="widthFix"></image>
      <text class="empty-text">暂无照片</text>
    </view>
    
    <view wx:else class="photo-grid">
      <view 
        wx:for="{{photoList}}" 
        wx:key="id" 
        class="photo-item" 
        bind:tap="viewPhotoDetail" 
        data-item="{{item}}"
      >
        <view class="photo-card">
          <!-- 服务前后对比图 -->
          <view class="photo-comparison">
            <view class="before-photo">
              <image src="{{item.beforePhoto}}" class="photo-image" mode="aspectFill"></image>
              <text class="photo-label">服务前</text>
            </view>
            <view class="after-photo">
              <image src="{{item.afterPhoto}}" class="photo-image" mode="aspectFill"></image>
              <text class="photo-label">服务后</text>
            </view>
          </view>
          
          <!-- 照片信息 -->
          <view class="photo-info">
            <view class="photo-title">{{item.title || item.petName + '的' + item.serviceTypeName}}</view>
            <view class="photo-meta">
              <text class="pet-info">{{item.petName}} · {{item.petType}}</text>
              <text class="service-type">{{item.serviceTypeName}}</text>
            </view>
            <view class="photo-stats">
              <view class="stat-item">
                <text class="icon-heart {{item.isLiked ? 'liked' : ''}}">♥</text>
                <text class="stat-text">{{item.likeCount}}</text>
              </view>
              <view class="stat-item">
                <text class="icon-view">👁</text>
                <text class="stat-text">{{item.viewCount}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view wx:if="{{hasMore}}" class="load-more" bind:tap="loadMore">
    <text wx:if="{{!loadingMore}}">加载更多</text>
    <text wx:else>加载中...</text>
  </view>


</view>
