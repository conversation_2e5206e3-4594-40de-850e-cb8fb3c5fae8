<view class="container container336456">
  <diy-navbar :isFixed="true" CustomBar='60' bgCustom="#fff">
    <view slot="content">
      <view class="flex align-center flex-nowrap diygw-col-24">
        <image src="//xian7.zos.ctyun.cn/pet/static/logo.png" class="response diygw-col-10" mode="widthFix"></image>
      </view>
    </view>
  </diy-navbar>
  <view class="flex diygw-col-24 swiper-clz">
    <swiper :current="swiperIndex" class="swiper" bindchange="changeSwiper" previous-margin="50rpx" next-margin="50rpx" indicator-color="rgba(51, 51, 51, 0.39)" indicator-active-color="#fff" indicator-dots="true" autoplay interval="3000" circular="true" style="height: 300rpx">
      <swiper-item class="diygw-swiper-item">
        <view class="diygw-swiper-item-wrap {{swiperIndex != 0?'swiper-scale':''}}">
          <image src="//xian7.zos.ctyun.cn/pet/static/group14.png" class="diygw-swiper-image"></image>
        </view>
      </swiper-item>
      <swiper-item class="diygw-swiper-item">
        <view class="diygw-swiper-item-wrap {{swiperIndex != 1?'swiper-scale':''}}">
          <image src="//xian7.zos.ctyun.cn/pet/static/group15.png" class="diygw-swiper-image"></image>
        </view>
      </swiper-item>
      <swiper-item class="diygw-swiper-item">
        <view class="diygw-swiper-item-wrap {{swiperIndex != 2?'swiper-scale':''}}">
          <image src="//xian7.zos.ctyun.cn/pet/static/group16.png" class="diygw-swiper-image"></image>
        </view>
      </swiper-item>
      <swiper-item class="diygw-swiper-item">
        <view class="diygw-swiper-item-wrap {{swiperIndex != 3?'swiper-scale':''}}">
          <image src="//xian7.zos.ctyun.cn/pet/static/group17.png" class="diygw-swiper-image"></image>
        </view>
      </swiper-item>
    </swiper>
  </view>
  <view class="flex diygw-col-24">
    <view class="diygw-grid col-4">
      <view class="diygw-grid-item">
        <view class="diygw-grid-inner" data-type="system" data-subtype="XI_HU" bind:tap="redirect">
          <view class="diygw-grid-icon diygw-avatar">
            <image mode="aspectFit" class="diygw-avatar-img" src="//xian7.zos.ctyun.cn/pet/static/grooming.png"></image>
          </view>
          <view class="diygw-grid-title">约洗护</view>
          <view class="diygw-grid-subtitle">定期护理更健康</view>
        </view>
      </view>
      <view class="diygw-grid-item" data-type="system" data-subtype="MEI_RONG" bind:tap="redirect">
        <view class="diygw-grid-inner">
          <view class="diygw-grid-icon diygw-avatar">
            <image mode="aspectFit" class="diygw-avatar-img" src="//xian7.zos.ctyun.cn/pet/static/veterinary.png"></image>
          </view>
          <view class="diygw-grid-title">约美容</view>
          <view class="diygw-grid-subtitle">B级美容师1对1</view>
        </view>
      </view>
      <view class="diygw-grid-item" data-type="system" data-subtype="WEI_YANG" bind:tap="redirect">
        <view class="diygw-grid-inner">
          <view class="diygw-grid-icon diygw-avatar">
            <image mode="aspectFit" class="diygw-avatar-img" src="//xian7.zos.ctyun.cn/pet/static/petfood.png"></image>
          </view>
          <view class="diygw-grid-title">约喂养</view>
          <view class="diygw-grid-subtitle">上门喂养更放心</view>
        </view>
      </view>
      <view class="diygw-grid-item" data-type="vip" bind:tap="redirect">
        <view class="diygw-grid-inner">
          <view class="diygw-grid-icon diygw-avatar">
            <image mode="aspectFit" class="diygw-avatar-img" src="//xian7.zos.ctyun.cn/pet/static/petcare.png"></image>
          </view>
          <view class="diygw-grid-title">权益卡</view>
          <view class="diygw-grid-subtitle">立享更多优惠</view>
        </view>
      </view>
    </view>
  </view>
  <view wx:if="{{tabsDatas.length}}" class="flex tabs diygw-col-24 flex-direction-column">
    <view class="diygw-tabs text-center solid-bottom justify-center scale-title small-border tabs-title">
      <view class="diygw-tab-item tabs-item-title  {{item.code ===tabsIndex ?'cur text-green':''}}" wx:for="{{tabsDatas}}" wx:key="code" wx:for-item="item" wx:for-index="index" data-key="index" data-name="{{item.name}}" data-index="{{item.code}}" catchtap="changeTabs">
        {{item.text}}
      </view>
    </view>
    <view class="tabContainer">
      <view class="flex-sub">
        <!-- <view class="diygw-col-24 tab-map" style="background-image: url(//xian7.zos.ctyun.cn/pet/static/map.png);" bind:tap="navigationMap"> -->
        <view class="diygw-col-24 tab-map" style="background-image: url(//xian7.zos.ctyun.cn/pet/static/map.png);">
          <view class="flex flex-wrap diygw-col-24 justify-between flex-tab-title">
            <view class="flex flex-wrap diygw-col-0 justify-center items-center">
              <text wx:if="{{vehicles.length > 0}}" class="diygw-text-line1 diygw-col-0">附近有{{vehicles.length}}辆车辆可约
              </text>
              <text wx:else class="diygw-text-line1 diygw-col-0">附近没有服务车辆</text>
            </view>
            <view class="flex flex-wrap diygw-col-0 items-center flex-tab-action hidden">
              <text class="diygw-col-0 text32-clz">去查看</text>
              <text class="flex icon1 diygw-col-0 diy-icon-right"></text>
            </view>
          </view>
        </view>
        <view class="flex diygw-col-24 items-center address-area-ly hidden">
          <view class="city-ly cityOverflowOmission">{{currentCity}}</view>
          <text class="flex icon1 diygw-col-0 diy-icon-titles"></text>
          <view class="adress-ly locationOverflowOmission">{{currentLocation}}</view>
          <text class="flex icon1 diygw-col-0 diy-icon-right"></text>
          <text class="flex icon6 diygw-col-0 diy-icon-locationfill" bind:tap="getLocation"></text>
        </view>
        <view class="flex diygw-col-24 button-group-ly margin-top">
          <button style="background-color: #ff4391 !important" class="diygw-btn red radius-xl flex-sub margin-lg button-button-clz" data-type="nursor" data-subtype="{{tabsIndex}}" bind:tap="redirect">
            指定{{tabsName}}员
          </button>
          <button style="background-color: #2f83ff !important" class="diygw-btn green radius-xl flex-sub margin-lg button-button-clz" data-type="system" data-subtype="{{tabsIndex}}" bind:tap="redirect">
            系统派单
          </button>
        </view>
      </view>
    </view>
  </view>
  <view class="image-wrapper-ly">
    <image src="//xian7.zos.ctyun.cn/pet/static/group12.png" class="response diygw-col-24" mode="widthFix" data-type="vip" bind:tap="redirect"></image>
  </view>

  <!-- 照片墙入口 -->
  <view class="photo-wall-entry-wrapper">
    <view class="photo-wall-entry-card" bind:tap="goToPhotoWall">
      <view class="photo-wall-content">
        <view class="photo-wall-left">
          <view class="photo-wall-icon">📸</view>
          <view class="photo-wall-text">
            <text class="photo-wall-title">照片墙</text>
            <text class="photo-wall-subtitle">精彩服务瞬间，见证爱宠蜕变</text>
          </view>
        </view>
        <view class="photo-wall-arrow">></view>
      </view>
    </view>
  </view>

  <!-- 测试文本 -->
  <view style="background: red; color: white; padding: 20rpx; margin: 20rpx 30rpx; text-align: center;">
    照片墙入口测试 - 如果你能看到这个红色区域，说明位置是正确的
  </view>
  <view class="flex flex-wrap diygw-col-24 flex-direction-column items-center flex16-clz">
    <view class="flex flex-wrap diygw-col-24 justify-between flex17-clz">
      <view class="flex flex-wrap diygw-col-0 justify-center items-center">
        <text class="diygw-text-line1 diygw-col-0 text13-clz">我的爱宠</text>
      </view>
      <view bind:tap="addPet">
        <view class="flex flex-wrap diygw-col-0 items-center flex30-clz">
          <text class="flex icon1 diygw-col-0 diy-icon-add"></text>
          <text class="diygw-col-0 text32-clz">添加宠物</text>
        </view>
      </view>
    </view>
    <scroll-view wx:if="{{pets.length}}" scroll-x class="flex scroll-view flex-wrap diygw-col-24 flex6-clz">
      <view class="flex flex-nowrap">
        <view wx:for="{{pets}}" wx:for-item="item" wx:for-index="index" wx:key="index" class="flex diygw-col-18 items-stretch flex-nowrap flex46-clz" style="background-color: {{item.gender===1 ? 'rgba(122, 221, 252, 0.3)':item.gender===0 ?'rgba(255, 251, 220, 1)':''}};" data-item="{{item}}" bind:tap="viewPetDetail">
          <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-center items-center">
            <image src="{{item.avatar||siteinfo.constant.pet[item.type]}}" class="image7-size diygw-image diygw-col-0 image7-clz" mode="widthFix"></image>
          </view>
          <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-between flex48-clz">
            <text class="diygw-col-0 text23-clz">{{item.name}}</text>
            <view class="flex flex-wrap diygw-col-24 flex49-clz">
              <text class="diygw-text-line1 diygw-col-0 text24-pet-ly">{{item.formattedBri}}</text>
              <text class="diygw-text-line1 diygw-col-0 text24-pet-ly">{{item.gender===0 ? '未知':item.gender===1?'弟弟':'妹妹'}}</text>
            </view>
            <view class="flex flex-wrap diygw-col-24 lastServiceInfo">
              <text class="lastServiceTime">{{item.formattedLastServiceTime}}</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
  <view class="clearfix"></view>
  <custom-modal show="{{showModal}}" bind:confirm="onModalConfirm" title="{{modalTitle}}" content="{{modalContent}}"></custom-modal>
  <custom-modal-ticket show="{{showTicketModal}}" />
  <custom-tabbar currentActive='home' />
</view>