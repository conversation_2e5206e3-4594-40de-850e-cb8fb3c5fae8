// pages/service/reservation/index.js
import addressApi from '../../../api/modules/address';
import siteinfo from '../../../siteinfo';
import serviceApi from '../../../api/modules/service';
import orderApi from '../../../api/modules/order';
import payApi from '../../../api/modules/pay';
import rightsCardApi from '../../../api/modules/rightsCard';
import couponApi from '../../../api/modules/coupon';
import dictionaryApi from '../../../api/modules/dictionary';
import utils from '../../utils/util';
import { OrderStatus } from '../../../common/constant';
import WeMessage from '../../../common/WeMessage';

// 价格计算和优惠管理逻辑已内联到页面中

Page({
  /**
   * 页面的初始数据
   */
  data: {
    siteinfo,
    modal: {
      isShow: false,
    },
    showModal: false,
    modalTitle: '温馨提示',
    modalContent: '请先返回上一页选择宠物信息后操作！',
    clickEvent: () => {
      wx.redirectTo({
        url: '/pages/serviceOrder/index',
      });
    },
    expectTime: null,
    clickTimeEvent: date => {
      wx.setStorageSync('expectServiceTime', date);
    },
    people: null,
    shop: {
      ids: [],
      totalNumber: 0,
      totalMoney: 0,
    },
    serviceInfo: null,
    petInfo: null,
    adressInfo: null,
    expectTime: null,
    peopleType: 'system',
    additionalServiceFee: '0.00', // 增项服务费用
    selectedServices: [], // 已选择的增项服务列表

    // 权益卡相关
    discountCards: [], // 折扣卡列表
    timesCards: [], // 次卡列表
    bestDiscountCard: null, // 最优折扣卡
    selectedTimesCard: null, // 已选择的次卡

    // 优惠券相关
    availableCoupons: [], // 可用优惠券列表
    selectedCoupon: null, // 已选择的优惠券

    // 价格计算
    originalPrice: 0, // 原始价格（未使用任何优惠前）
    discountedPrice: 0, // 折扣后价格
    finalPrice: 0, // 最终价格
    discountAmount: 0, // 折扣金额
    couponAmount: 0, // 优惠券抵扣金额

    // 优惠选择
    showDiscountSelector: false, // 是否显示优惠选择器
    discountType: 'none', // 优惠类型：none-不使用优惠，times-使用次卡，coupon-使用优惠券
    discountSelectorTab: 'none', // 优惠选择器当前标签
    selectedCouponId: '', // 已选择的优惠券ID
    selectedTimesCardId: '', // 已选择的次卡ID
    tabsDatas: [
      {
        text: `增项服务`,
        icon: ``,
      },
      {
        text: `服务介绍`,
        icon: ``,
      },
      {
        text: `预约须知`,
        icon: ``,
      },
      {
        text: `服务评价`,
        icon: ``,
      },
    ],
    tabsIndex: 0,
    addService: [],
    serviceIntro: '//xian7.zos.ctyun.cn/pet/static/g-sg-xh.png',

    // 用户备注
    userRemark: '', // 用户备注信息
  },
  onLoad(option) {
    this.getAdress();
    const dictionary = wx.getStorageSync('dictionary_com');
    // TOSO 这里参数应该改为选中的服务id
    serviceApi.getAdditionalService(option.serviceId).then(res => {
      const originalArray = res || [];
      const groupedArray = originalArray.reduce((acc, current) => {
        // 如果累加器中还没有这个type的数组，就创建一个
        if (!acc[current.type]) {
          acc[current.type] = {
            name: dictionary.find(v => v.code === current.type).name,
            id: current.type,
            children: [],
          };
        }
        // 将当前对象push到对应type的数组中
        acc[current.type].children.push(current);
        return acc;
      }, {});
      // 转换为数组
      const groupedList = Object.values(groupedArray);
      this.setData({
        addService: groupedList || [],
      });
    });
  },
  onShow() {
    const petItem = wx.getStorageSync('selectPetInfo');
    const adressItem = wx.getStorageSync('selectAdressInfo');
    const serviceItem = wx.getStorageSync('selectServiceInfo');
    const selectedEmployee = wx.getStorageSync('selectedEmployee');
    // const timeItem = wx.getStorageSync('expectServiceTime');
    let imgUrl = '//xian7.zos.ctyun.cn/pet/static/gglsb_xh.png';
    if (serviceItem) {
      const { petTypes, currentType, serviceName } = serviceItem;
      const { type, subtype } = currentType;
      if (petTypes === 'dog') {
        if (type === '格伦仕宝') {
          if (subtype === '洗护') {
            imgUrl = '//xian7.zos.ctyun.cn/pet/static/gglsb-xh.png';
          }
          if (subtype === '美容') {
            if (serviceName.indexOf('剃剪') > -1) {
              imgUrl = '//xian7.zos.ctyun.cn/pet/static/gglsb-tj.png';
            }
            if (serviceName.indexOf('手工剪') > -1) {
              imgUrl = '//xian7.zos.ctyun.cn/pet/static/gglsb-sgj.png';
            }
          }
        }
        if (type === '伊珊娜') {
          if (subtype === '洗护') {
            imgUrl = '//xian7.zos.ctyun.cn/pet/static/gysn-xh.png';
          }
          if (subtype === '美容') {
            if (serviceName.indexOf('剃剪') > -1) {
              imgUrl = '//xian7.zos.ctyun.cn/pet/static/gysn-tj.png';
            }
            if (serviceName.indexOf('手工剪') > -1) {
              imgUrl = '//xian7.zos.ctyun.cn/pet/static/gysn-sgj.png';
            }
          }
        }
      }
      if (petTypes === 'cat') {
        if (type === '格伦仕宝') {
          imgUrl = '//xian7.zos.ctyun.cn/pet/static/mglsb-xh.png';
        }
        if (type === '伊珊娜') {
          imgUrl = '//xian7.zos.ctyun.cn/pet/static/mysn-xh.png';
        }
      }
    }
    // 计算初始增项服务费用（此时没有增项服务，所以为0）
    const additionalServiceFee = '0.00';
    const originalPrice = serviceItem ? Number(serviceItem.basePrice) : 0;

    // 如果有选中的员工，处理职位信息
    if (selectedEmployee) {
      this.processEmployeePosition(selectedEmployee);
    }

    this.setData({
      petInfo: petItem,
      adressInfo: adressItem,
      serviceInfo: serviceItem,
      serviceIntro: imgUrl,
      people: selectedEmployee, // 设置选中的员工信息
      showService: !!selectedEmployee, // 如果有选中员工则显示服务人员信息
      peopleType: selectedEmployee ? 'selected' : 'system', // 设置人员类型
      shop: {
        ids: [],
        totalNumber: 0,
        totalMoney: serviceItem.basePrice,
      },
      additionalServiceFee,
      selectedServices: [], // 初始化已选择的增项服务列表
      originalPrice,
      discountedPrice: originalPrice,
      finalPrice: originalPrice,
      discountAmount: 0,
      couponAmount: 0,
      discountType: 'none',
    });

    // 页面加载时立即获取优惠券和权益卡信息（仅查询一次）
    if (this.data.userInfo && this.data.userInfo.id && serviceItem) {
      console.log('页面加载，立即获取可用优惠券和权益卡（仅查询一次）');
      this.loadUserDiscounts(this.data.userInfo.id, serviceItem.id, originalPrice);
    }
  },

  /**
   * 处理员工职位和其他信息
   */
  async processEmployeePosition(employee) {
    if (!employee) {
      return;
    }

    try {
      // 并行获取职位字典和车辆类型字典
      const [positionList, vehicleTypeList] = await Promise.all([
        dictionaryApi.list("员工职位"),
        dictionaryApi.list("车辆类型")
      ]);

      // 处理职位信息
      if (employee.position && positionList && positionList.length > 0) {
        const position = positionList.find(item => item.code === employee.position);
        if (position) {
          employee.positionName = position.name;
        }
      }

      // 处理工作经验
      if (employee.workExp) {
        employee.formattedWorkExp = this.formatWorkExperience(employee.workExp);
      }

      // 处理车辆类型信息
      if (employee.vehicle && employee.vehicle.vehicleType && vehicleTypeList && vehicleTypeList.length > 0) {
        const vehicleType = vehicleTypeList.find(item => item.code === employee.vehicle.vehicleType);
        if (vehicleType) {
          employee.vehicle.vehicleTypeName = vehicleType.name;
        }
      }

      this.setData({
        people: employee
      });
    } catch (error) {
      console.error('获取字典失败:', error);
    }
  },

  /**
   * 将月数转换为年月格式
   */
  formatWorkExperience(months) {
    if (!months || months <= 0) {
      return '';
    }

    const years = Math.floor(months / 12);
    const remainingMonths = months % 12;

    if (years === 0) {
      return `${remainingMonths}个月经验`;
    } else if (remainingMonths === 0) {
      return `${years}年经验`;
    } else {
      return `${years}年${remainingMonths}个月经验`;
    }
  },

  /**
   * 加载用户的权益卡和优惠券信息
   * @param {string} customerId 用户ID
   * @param {string} serviceId 服务ID
   * @param {number} amount 订单金额
   */
  async loadUserDiscounts(customerId, serviceId, amount) {
    try {
      console.log('开始加载优惠信息（仅查询一次）', customerId, serviceId, amount);

      // 获取服务可用的权益卡
      console.log('获取服务可用的权益卡...');
      const availableCards = await rightsCardApi.getAvailableCards(customerId, serviceId, amount);
      console.log('获取到可用权益卡:', availableCards);

      // 分类权益卡
      let discountCards = [];
      let timesCards = [];

      if (availableCards && availableCards.list && availableCards.list.length > 0) {
        // 将权益卡分为折扣卡和次卡
        availableCards.list.forEach(card => {
          if (card.cardType.type === 'discount') {
            discountCards.push({ ...card, expiryTime: utils.formatNormalDate(card.expiryTime) });
          } else if (card.cardType.type === 'times') {
            timesCards.push({
              ...card,
              expiryTime: utils.formatNormalDate(card.expiryTime),
            });
          }
        });
        console.log('折扣卡:', discountCards);
        console.log('次卡:', timesCards);

        // 找出最优的折扣卡（折扣力度最大的）
        let bestDiscountCard = null;
        if (discountCards.length > 0) {
          bestDiscountCard = discountCards.reduce((best, current) => {
            // 折扣率越小，优惠力度越大
            return best.discountRate < current.discountRate ? best : current;
          }, discountCards[0]);
          console.log('最优折扣卡:', bestDiscountCard);
        }

        this.setData({
          discountCards,
          timesCards,
          bestDiscountCard,
        });

        // 如果有最优折扣卡，自动应用折扣
        if (bestDiscountCard) {
          this.applyDiscountCard(bestDiscountCard);
        }
      } else {
        console.log('没有可用的权益卡');
        this.setData({
          discountCards: [],
          timesCards: [],
          bestDiscountCard: null,
        });
      }

      // 获取服务可用的优惠券
      console.log('获取服务可用的优惠券...');
      const availableCoupons = await couponApi.getAvailableCoupons(customerId, serviceId, amount);
      console.log('获取到可用优惠券:', availableCoupons);

      if (availableCoupons && availableCoupons.list && availableCoupons.list.length > 0) {
        this.setData({
          availableCoupons: availableCoupons.list.map(card => ({
            ...card,
            threshold: Number(card.coupon.threshold || '0'),
            amount: Number(card.coupon.amount || '0'),
            expiryTime: utils.formatNormalDate(card.expiryTime),
          })),
        });
      } else {
        console.log('没有可用的优惠券');
        this.setData({
          availableCoupons: [],
        });
      }

      wx.hideLoading();
    } catch (error) {
      console.error('加载优惠信息失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '加载优惠信息失败',
        icon: 'none',
      });
    }
  },

  /**
   * 计算折扣卡折扣后的价格
   * @param {number} originalPrice 原始价格
   * @param {object} discountCard 折扣卡信息
   * @returns {object} 折扣后的价格和折扣金额
   */
  calculateDiscountCardPrice(originalPrice, discountCard) {
    let discountedPrice = originalPrice;
    let discountAmount = 0;

    if (discountCard) {
      const discountRate = Number(discountCard.discountRate) || 1;
      discountedPrice = (originalPrice * discountRate).toFixed(2);
      discountAmount = (originalPrice - discountedPrice).toFixed(2);
    }

    return {
      discountedPrice,
      discountAmount,
    };
  },

  /**
   * 应用折扣卡
   * @param {Object} discountCard 折扣卡信息
   */
  applyDiscountCard(discountCard) {
    if (!discountCard) return;

    // 检查折扣卡是否已被使用
    if (discountCard.isUsed) {
      return;
    }

    // 使用价格计算服务计算折扣后的价格
    const { originalPrice } = this.data;
    const { discountedPrice, discountAmount } = this.calculateDiscountCardPrice(originalPrice, discountCard);

    // 更新折扣卡状态为已使用
    discountCard.isUsed = true;

    this.setData({
      discountedPrice,
      finalPrice: discountedPrice,
      discountAmount,
      bestDiscountCard: discountCard, // 更新当前使用的折扣卡
    });

    // 更新订单总金额
    this.updateTotalMoney();
  },
  async getAdress() {
    const userInfo = this.data.userInfo;
    if (!userInfo) {
      this.setData({
        adressInfo: null,
      });
      return;
    }
    const curAdress = wx.getStorageSync('selectAdressInfo');
    if (!curAdress) {
      addressApi.getDefault(userInfo.id).then(res => {
        wx.setStorageSync('selectAdressInfo', res);
        this.setData({
          adressInfo: res,
        });
      });
    }
  },
  redirect(evt) {
    let { type } = evt.currentTarget.dataset;
    wx.navigateTo({
      url: '/pages/service/' + type + '/index?type=sevice',
    });
  },

  /**
   * 更换服务人员
   */
  changeEmployee() {
    wx.navigateTo({
      url: '/pages/service/nursor/index',
    });
  },
  changeTabs(evt) {
    let { index } = evt.currentTarget.dataset;
    if (index == this.data.tabsIndex) return;
    this.setData({
      tabsIndex: index,
    });
  },
  checkAndNavigate() {
    // 检查是否有宠物信息
    if (!this.data.petInfo) {
      // 显示自定义模态框
      this.setData({
        showModal: true,
      });
      return false;
    }
    return true;
  },
  pleaseClick(evt) {
    let { open } = evt.currentTarget.dataset;
    if (this.checkAndNavigate()) {
      this.setData({
        modal: {
          isShow: open === 'open',
        },
      });
    }
  },
  preventClick() {},
  /**
   * 增加或减少增项服务
   */
  addService(evt) {
    if (this.checkAndNavigate()) {
      let { price, id, pid } = evt.currentTarget.dataset;
      let ids = this.data.shop.ids,
        totalNumber = this.data.shop.totalNumber,
        totalMoney = Number(this.data.originalPrice),
        isAdd;
      if (this.data.shop.ids.indexOf(id) > -1) {
        ids = utils.removeValue(ids, id);
        totalNumber = totalNumber - 1;
        totalMoney = (totalMoney - Number(price)).toFixed(2);
        isAdd = 'minus';
      } else {
        ids = ids.concat([id]);
        totalNumber = totalNumber + 1;
        totalMoney = (totalMoney + Number(price)).toFixed(2);
        isAdd = 'add';
      }
      const services = this.data.addService;
      const newArr = services.map(v => {
        if (v.id === pid) {
          const { children, ...rest } = v;
          const newVal = v.children.map(t => {
            if (t.id === id) {
              return {
                ...t,
                isAdd,
              };
            } else {
              return t;
            }
          });
          return {
            ...rest,
            children: newVal,
          };
        }
        return v;
      });

      // 更新原始价格（包含增项服务）
      const originalPrice = Number(totalMoney);
      console.log('更新后的原始价格', originalPrice);

      // 计算增项服务费用
      let additionalServiceFee = '0.00';
      if (this.data.serviceInfo && totalMoney > 0) {
        const basePrice = Number(this.data.serviceInfo.basePrice) || 0;
        const total = Number(totalMoney) || 0;
        if (total > basePrice) {
          additionalServiceFee = (total - basePrice).toFixed(2);
        }
      }

      this.setData({
        addService: newArr,
        shop: {
          ids,
          totalNumber,
        },
        originalPrice,
        additionalServiceFee, // 更新增项服务费用
      });

      // 增项服务变化时只更新价格，不重新获取优惠券和权益卡
      this.updateTotalMoney();
    }
  },

  /**
   * 计算优惠券折扣后的价格
   * @param {number} discountedPrice 已经应用折扣卡后的价格
   * @param {object} coupon 优惠券信息
   * @returns {object} 优惠券折扣后的价格和优惠券折扣金额
   */
  calculateCouponPrice(discountedPrice, coupon) {
    let finalPrice = discountedPrice;
    let couponAmount = 0;

    if (coupon) {
      // 获取优惠券金额，尝试多个可能的字段名
      const deduction = Number(coupon.amount || coupon.value || coupon.deduction || 0);
      finalPrice = Math.max(0, discountedPrice - deduction).toFixed(2);
      couponAmount = (discountedPrice - finalPrice).toFixed(2);
    }

    return {
      finalPrice,
      couponAmount,
    };
  },

  /**
   * 计算次卡折扣后的价格
   * @param {number} discountedPrice 已经应用折扣卡后的价格
   * @returns {object} 次卡折扣后的价格和次卡折扣金额
   */
  calculateTimesCardPrice(discountedPrice) {
    let finalPrice = 0; // 次卡直接抵扣全部费用
    let couponAmount = discountedPrice;

    return {
      finalPrice,
      couponAmount,
    };
  },

  /**
   * 更新订单总金额
   */
  updateTotalMoney() {
    const { originalPrice, bestDiscountCard, selectedCoupon, selectedTimesCard, discountType } = this.data;

    console.log('更新订单总金额', {
      originalPrice,
      bestDiscountCard,
      selectedCoupon,
      selectedTimesCard,
      discountType,
    });

    // 首先应用折扣卡
    const { discountedPrice, discountAmount } = this.calculateDiscountCardPrice(originalPrice, bestDiscountCard);

    // 然后根据折扣类型应用优惠券或次卡
    let finalPrice = discountedPrice;
    let couponAmount = 0;

    if (discountType === 'times' && selectedTimesCard) {
      // 使用次卡
      const result = this.calculateTimesCardPrice(discountedPrice);
      finalPrice = result.finalPrice;
      couponAmount = result.couponAmount;
    } else if (discountType === 'coupon' && selectedCoupon) {
      // 使用优惠券
      const result = this.calculateCouponPrice(discountedPrice, selectedCoupon);
      finalPrice = result.finalPrice;
      couponAmount = result.couponAmount;
    }

    console.log('价格计算结果', {
      originalPrice,
      discountedPrice,
      finalPrice,
      discountAmount,
      couponAmount,
    });

    // 更新价格数据
    this.setData({
      discountedPrice,
      finalPrice,
      discountAmount,
      couponAmount,
      shop: {
        ...this.data.shop,
        totalMoney: parseFloat(finalPrice).toFixed(2), // 确保 totalMoney 格式正确
      },
    });
  },
  /**
   * 主支付流程控制
   */
  async payOrder() {
    const { userInfo } = this.data;
    if (!userInfo) {
      return;
    }

    try {
      wx.showLoading({
        title: '处理中',
      });

      // 构建订单信息
      const orderInfo = this.buildOrderInfo();
      if (!orderInfo) {
        wx.hideLoading();
        return;
      }

      // 查询待付款订单
      const pendingOrders = await orderApi.getlists(userInfo.id, OrderStatus.待付款);
      const existingOrder = this.findMatchingOrder(pendingOrders, orderInfo);

      console.log('找到匹配的订单:', existingOrder);

      if (existingOrder) {
        // 检查是否需要更新订单
        const needsUpdate = this.checkIfOrderNeedsUpdate(existingOrder, orderInfo);

        if (needsUpdate) {
          console.log('订单需要更新，删除旧订单并创建新订单');
          await this.updateExistingOrder(existingOrder, orderInfo);
        } else {
          console.log('订单无需更新，直接支付');
          await this.processExistingOrder(existingOrder);
        }
      } else {
        console.log('创建新订单');
        await this.createNewOrder(orderInfo);
      }

    } catch (error) {
      wx.hideLoading();
      console.error('支付过程发生错误:', error);
      wx.showToast({
        title: '操作失败，请稍后重试',
        icon: 'none',
      });
    }
  },

  /**
   * 构建订单信息
   */
  buildOrderInfo() {
    const { petInfo, adressInfo, serviceInfo, shop, userInfo, bestDiscountCard, selectedCoupon, selectedTimesCard, discountType, discountAmount, couponAmount, originalPrice, people, userRemark } = this.data;
    const expectTime = wx.getStorageSync('expectServiceTime');

    // 判断地址
    if (!adressInfo) {
      this.navigateTo({
        type: 'tip',
        tip: '请先添加地址',
      });
      return null;
    }

    const orderInfo = {
      /** 客户ID */
      customerId: userInfo.id,
      /** 下单时间 */
      orderTime: new Date(),
      /** 原价（未使用任何优惠前的价格） */
      originalPrice,
      /** 订单总费用 */
      totalFee: shop.totalMoney,
      /** 权益卡抵扣金额 */
      cardDeduction: discountAmount || 0,
      /** 优惠券抵扣金额 */
      couponDeduction: couponAmount || 0,
      /** 地址ID */
      addressId: adressInfo.id,
      /** 服务地址 */
      address: adressInfo.addressText,
      /** 服务地址经度 */
      longitude: adressInfo.longitude,
      /** 服务地址纬度 */
      latitude: adressInfo.latitude,
      /** 服务地址详情 */
      addressDetail: adressInfo.detailAddress,
      /** 服务地址备注 */
      addressRemark: adressInfo.remark,
      /** 订单明细列表 */
      orderDetails: [
        {
          /** 服务ID */
          serviceId: serviceInfo.id,
          /** 宠物ID */
          petId: petInfo.id,
          /** 宠物名称 */
          petName: petInfo.name,
          /** 宠物类型 */
          petType: petInfo.type,
          /** 宠物品种 */
          petBreed: petInfo.breed,
          /** 下单时间 */
          orderTime: new Date(),
          /** 增项服务ID列表 */
          addServiceIds: shop.ids,
          /** 用户备注 */
          userRemark: userRemark || '',
        },
      ],
      /** 优惠信息 */
      discountInfos: [],
    };

    // 如果有选中的员工，添加员工ID
    if (people && people.id) {
      orderInfo.employeeId = people.id;
    }

    // 判断时间
    if (expectTime) {
      orderInfo.serviceTime = new Date(expectTime);
    }

    // 添加优惠信息
    if (discountType === 'coupon' && selectedCoupon) {
      orderInfo.discountInfos.push({
        discountType: 'coupon',
        discountId: selectedCoupon.id,
        discountAmount: Number(couponAmount) || 0,
      });
    } else if (discountType === 'times' && selectedTimesCard) {
      orderInfo.discountInfos.push({
        discountType: 'membership_card',
        discountId: selectedTimesCard.id,
        discountAmount: Number(couponAmount) || 0,
      });
    }

    // 如果有折扣卡优惠且不为0
    if (bestDiscountCard && Number(discountAmount) > 0) {
      orderInfo.discountInfos.push({
        discountType: 'membership_card',
        discountId: bestDiscountCard.id,
        discountAmount: Number(discountAmount) || 0,
      });
    }

    return orderInfo;
  },

  /**
   * 查找匹配的订单
   */
  findMatchingOrder(pendingOrders, orderInfo) {
    if (!pendingOrders || !Array.isArray(pendingOrders)) {
      console.log('没有找到待付款订单或返回格式不正确');
      return null;
    }

    const detail = orderInfo.orderDetails[0];
    console.log('待付款订单列表:', pendingOrders);
    console.log('当前订单详情:', detail);

    return pendingOrders.find(order => {
      // 检查订单详情是否存在
      if (!order.orderDetails || !order.orderDetails[0]) {
        return false;
      }

      const orderDetail = order.orderDetails[0];
      const isSameService = orderDetail.serviceId === detail.serviceId;
      const isSamePet = orderDetail.petId === detail.petId;

      console.log('订单匹配检查:', {
        orderId: order.id,
        isSameService,
        isSamePet,
        orderServiceId: orderDetail.serviceId,
        currentServiceId: detail.serviceId,
        orderPetId: orderDetail.petId,
        currentPetId: detail.petId,
      });

      // 只要服务和宠物相同就认为是匹配的订单，不再比较金额和增项服务
      return isSameService && isSamePet;
    });
  },

  /**
   * 检查订单是否需要更新
   */
  checkIfOrderNeedsUpdate(existingOrder, orderInfo) {
    const orderDetail = existingOrder.orderDetails[0];
    const currentDetail = orderInfo.orderDetails[0];

    // 金额比较
    const orderTotal = parseFloat(existingOrder.totalFee).toFixed(2);
    const currentTotal = parseFloat(orderInfo.totalFee).toFixed(2);
    const amountChanged = orderTotal !== currentTotal;

    // 增项服务比较
    const orderAddServices = orderDetail.addServiceIds || [];
    const currentAddServices = currentDetail.addServiceIds || [];

    const addServicesChanged =
      orderAddServices.length !== currentAddServices.length ||
      !orderAddServices.every(id => currentAddServices.includes(id)) ||
      !currentAddServices.every(id => orderAddServices.includes(id));

    console.log('订单更新检查:', {
      orderId: existingOrder.id,
      amountChanged,
      addServicesChanged,
      orderTotal,
      currentTotal,
      orderAddServices,
      currentAddServices,
    });

    return amountChanged || addServicesChanged;
  },

  /**
   * 更新现有订单（通过删除重建实现）
   */
  async updateExistingOrder(existingOrder, orderInfo) {
    const { userInfo } = this.data;

    try {
      // 删除旧订单
      console.log('删除旧订单:', existingOrder.id);
      await orderApi.cancel(userInfo.id, existingOrder.id);

      // 创建新订单
      await this.createNewOrder(orderInfo);
    } catch (error) {
      console.error('更新订单失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '更新订单失败，请稍后重试',
        icon: 'none',
      });
    }
  },

  /**
   * 处理已存在的订单
   */
  async processExistingOrder(existingOrder) {
    wx.hideLoading();
    await this.handlePayment(existingOrder);
  },

  /**
   * 创建新订单
   */
  async createNewOrder(orderInfo) {
    const { userInfo } = this.data;

    try {
      // 生成订单
      const order = await orderApi.add(userInfo.id, orderInfo);
      wx.hideLoading();

      if (!order) {
        this.navigateTo({
          type: 'tip',
          tip: '创建订单失败，请稍后再试！',
        });
        return;
      }

      await this.handlePayment(order);
    } catch (error) {
      console.error('创建订单失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '创建订单失败，请稍后重试',
        icon: 'none',
      });
    }
  },

  /**
   * 处理支付逻辑
   */
  async handlePayment(order) {
    const { userInfo } = this.data;

    try {
      // 处理0元订单
      if (Number(order.totalFee) === 0) {
        wx.showLoading({
          title: '处理中',
        });
        const res = await orderApi.handleZeroAmountOrder(userInfo.id, order.sn);
        wx.hideLoading();

        if (res.success) {
          console.log('0元订单处理成功:', userInfo);
          this.handlePaymentSuccessModal(userInfo.openid, order.id);
        } else {
          wx.showToast({
            title: res.error || '处理失败，请稍后重试',
            icon: 'none',
          });
        }
        return;
      }

      // 正常支付流程
      payApi.doPay({
        sn: order.sn,
        onOk: () => {
          // 支付成功后，调用支付成功接口更新订单状态
          orderApi.pay(userInfo.id, order.sn).then(() => {
            console.log('支付成功:', userInfo);
            this.handlePaymentSuccessModal(userInfo.openid, order.id);
          });
        },
        onCancel: () => {
          wx.showToast({
            title: '取消支付',
            icon: 'none',
          });
        },
        onError: () => {
          wx.showToast({
            title: '支付失败',
            icon: 'none',
          });
        },
        complete: () => {},
      });
    } catch (error) {
      console.error('支付处理失败:', error);
      wx.showToast({
        title: '支付处理失败，请稍后重试',
        icon: 'none',
      });
    }
  },

  redirectPages() {
    wx.navigateTo({
      url: '/pages/service/nursor/index',
    });
  },

  /**
   * 显示优惠选择器
   */
  showDiscountSelector() {
    // 使用 setTimeout 延迟设置 showDiscountSelector 属性
    setTimeout(() => {
      this.setData(
        {
          showDiscountSelector: true,
        },
        () => {
          console.log('设置 showDiscountSelector 为 true');
        }
      );
    }, 100);
  },

  /**
   * 隐藏优惠选择器
   */
  hideDiscountSelector() {
    // 使用 setTimeout 延迟设置 showDiscountSelector 属性
    setTimeout(() => {
      this.setData(
        {
          showDiscountSelector: false,
        },
        () => {
          console.log('设置 showDiscountSelector 为 false');
        }
      );
    }, 100);
  },

  /**
   * 切换优惠选择器标签
   */
  switchDiscountTab(e) {
    const { tab } = e.currentTarget.dataset;
    console.log('切换标签', tab);

    this.setData({
      discountSelectorTab: tab,
    });

    // 切换标签时更新预览价格
    this.previewDiscountPrice(tab);

    // 直接设置当前预览的折扣类型
    this.data.currentPreviewDiscountType = tab;

    // 如果切换到"不使用"标签，清空选择
    if (tab === 'none') {
      this.setData({
        selectedCouponId: '',
        selectedTimesCardId: '',
      });
    }
  },

  /**
   * 选择优惠券，支持再次点击取消选中
   */
  selectCoupon(e) {
    const { id } = e.currentTarget.dataset;
    const { selectedCouponId } = this.data;

    console.log('选择优惠券', id);

    // 如果已经选中了这个优惠券，再次点击取消选中
    if (selectedCouponId === id) {
      this.setData({
        selectedCouponId: '',
      });
      console.log('取消选中优惠券', id);
    } else {
      this.setData({
        selectedCouponId: id,
      });
      console.log('选中优惠券', id);
    }

    // 立即计算并显示优惠后的金额
    // 强制使用 'coupon' 作为折扣类型，确保正确计算
    this.previewDiscountPrice('coupon');

    // 直接设置当前预览的折扣类型
    this.data.currentPreviewDiscountType = 'coupon';
  },

  /**
   * 选择次卡，支持再次点击取消选中
   */
  selectTimesCard(e) {
    const { id } = e.currentTarget.dataset;
    const { selectedTimesCardId } = this.data;

    console.log('选择次卡', id);

    // 如果已经选中了这个次卡，再次点击取消选中
    if (selectedTimesCardId === id) {
      this.setData({
        selectedTimesCardId: '',
      });
      console.log('取消选中次卡', id);
    } else {
      this.setData({
        selectedTimesCardId: id,
      });
      console.log('选中次卡', id);
    }

    // 立即计算并显示优惠后的金额
    // 强制使用 'times' 作为折扣类型，确保正确计算
    this.previewDiscountPrice('times');

    // 直接设置当前预览的折扣类型
    this.data.currentPreviewDiscountType = 'times';
  },

  /**
   * 预览优惠后的价格
   * @param {string} discountType 优惠类型：'none', 'coupon', 'times'
   */
  previewDiscountPrice(discountType) {
    const { originalPrice, bestDiscountCard, selectedCouponId, selectedTimesCardId, availableCoupons, timesCards } =
      this.data;

    console.log('预览价格计算开始', {
      discountType,
      originalPrice,
      bestDiscountCard,
      selectedCouponId,
      selectedTimesCardId,
      availableCoupons: availableCoupons ? availableCoupons.length : 0,
      timesCards: timesCards ? timesCards.length : 0,
    });

    // 首先应用折扣卡
    const { discountedPrice } = this.calculateDiscountCardPrice(originalPrice, bestDiscountCard);

    // 然后根据折扣类型应用优惠券或次卡
    let previewFinalPrice = discountedPrice;

    if (discountType === 'times' && selectedTimesCardId) {
      // 使用次卡
      previewFinalPrice = 0;
    } else if (discountType === 'coupon' && selectedCouponId) {
      // 使用优惠券
      const selectedCoupon = availableCoupons.find(item => item.id === selectedCouponId);
      if (selectedCoupon) {
        const { finalPrice } = this.calculateCouponPrice(discountedPrice, selectedCoupon);
        previewFinalPrice = finalPrice;
      }
    }

    console.log('预览价格计算结果', {
      previewFinalPrice,
      discountedPrice,
    });

    // 使用两次 setData 来强制更新视图
    this.setData(
      {
        previewFinalPrice: null,
        displayPrice: '计算中...',
      },
      () => {
        // 在回调中再次设置，确保视图更新
        this.setData(
          {
            previewFinalPrice,
            displayPrice: previewFinalPrice,
          },
          () => {
            console.log('更新后的预览价格', this.data.previewFinalPrice, '显示价格', this.data.displayPrice);
          }
        );
      }
    );

    // 保存当前的折扣类型，以便在确认时使用
    // 使用同步方式设置，避免与其他 setData 冲突
    this.data.currentPreviewDiscountType = discountType;
  },

  /**
   * 查找优惠券
   * @param {string} couponId 优惠券ID
   * @param {array} availableCoupons 可用的优惠券列表
   * @returns {object|null} 找到的优惠券或null
   */
  findCoupon(couponId, availableCoupons) {
    if (!couponId || !availableCoupons || availableCoupons.length === 0) {
      return null;
    }
    return availableCoupons.find(item => item.id === couponId);
  },

  /**
   * 查找次卡
   * @param {string} timesCardId 次卡ID
   * @param {array} timesCards 可用的次卡列表
   * @returns {object|null} 找到的次卡或null
   */
  findTimesCard(timesCardId, timesCards) {
    if (!timesCardId || !timesCards || timesCards.length === 0) {
      return null;
    }
    return timesCards.find(item => item.id === timesCardId);
  },

  /**
   * 确定折扣类型和选中的优惠
   * @param {object} params 参数
   * @param {string} params.currentDiscountType 当前折扣类型
   * @param {string} params.selectedCouponId 选中的优惠券ID
   * @param {string} params.selectedTimesCardId 选中的次卡ID
   * @param {array} params.availableCoupons 可用的优惠券列表
   * @param {array} params.timesCards 可用的次卡列表
   * @returns {object} 确定的折扣类型和选中的优惠
   */
  determineDiscount(params) {
    const { currentDiscountType, selectedCouponId, selectedTimesCardId, availableCoupons, timesCards } = params;

    let discountType = currentDiscountType || 'none';
    let selectedCoupon = null;
    let selectedTimesCard = null;

    if (discountType === 'coupon' && selectedCouponId) {
      selectedCoupon = this.findCoupon(selectedCouponId, availableCoupons);
      if (!selectedCoupon) {
        discountType = 'none';
      }
    } else if (discountType === 'times' && selectedTimesCardId) {
      selectedTimesCard = this.findTimesCard(selectedTimesCardId, timesCards);
      if (!selectedTimesCard) {
        discountType = 'none';
      }
    } else {
      discountType = 'none';
    }

    return {
      discountType,
      selectedCoupon,
      selectedTimesCard,
    };
  },

  /**
   * 优惠选择器确认回调
   */
  onDiscountConfirm(e) {
    const { discountType, selectedCoupon, selectedTimesCard } = e.detail;

    console.log('优惠选择器确认', {
      discountType,
      selectedCoupon,
      selectedTimesCard,
    });

    // 更新数据
    this.setData(
      {
        discountType,
        selectedCoupon,
        selectedTimesCard,
      },
      () => {
        // 更新价格
        this.updateTotalMoney();

        // 使用 setTimeout 延迟设置 showDiscountSelector 属性
        setTimeout(() => {
          this.setData(
            {
              showDiscountSelector: false,
            },
            () => {
              console.log('设置 showDiscountSelector 为 false');
            }
          );
        }, 100);
      }
    );
  },

  /**
   * 阻止滑动穿透
   */
  preventTouchMove() {
    return false;
  },

  /**
   * 格式化金额，保留两位小数
   * @param {number} amount 金额
   * @returns {string} 格式化后的金额
   */
  formatAmount(amount) {
    return Number(amount).toFixed(2);
  },

  /**
   * 处理支付成功后的弹窗 - 服务预约页面
   * @param {string} openId 用户openId
   * @param {string} sn 订单编号
   */
  handlePaymentSuccessModal(openId, sn) {
    const weMessage = new WeMessage(openId, sn, OrderStatus.待接单);
    const modalConfig = weMessage.handlePaymentSuccess();
    if (modalConfig) {
      this.setData({
        showModal: true,
        modalTitle: modalConfig.modalConfig.title,
        modalContent: modalConfig.modalConfig.content,
        modalButtons: modalConfig.modalConfig.buttons
      });
      this._weMessage = weMessage;
    }
  },

  // 处理订阅确认按钮点击
  handleModalConfirm(e) {
    if (this._weMessage) {
      this._weMessage.requestOrderConfirmationSubscribe();
    }
    this.setData({ showModal: false });
    this.data.clickEvent && this.data.clickEvent();
  },

  // 处理订阅取消按钮点击
  handleModalCancel(e) {
    if (this._weMessage) {
      this._weMessage.recordUserChoice(false);
    }
    this.setData({ showModal: false });
    this.data.clickEvent && this.data.clickEvent();
  },

  /**
   * 处理用户备注输入
   */
  onUserRemarkInput(e) {
    this.setData({
      userRemark: e.detail.value
    });
  },
});
