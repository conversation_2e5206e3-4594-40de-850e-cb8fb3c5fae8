<!-- pages/complaint/index.wxml -->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">{{isEditMode ? '编辑投诉建议' : '投诉建议'}}</text>
    <view class="header-actions" wx:if="{{!isEditMode}}">
      <text class="history-btn" bindtap="viewHistory">历史记录</text>
    </view>
  </view>

  <!-- 分类选择 -->
  <view class="category-section">
    <view class="section-title">
      <text>请选择类型</text>
    </view>
    <view class="category-container">
      <view 
        wx:for="{{categoryOptions}}" 
        wx:key="value" 
        class="category-item {{category === item.value ? 'active' : ''}}"
        bindtap="selectCategory"
        data-category="{{item.value}}"
      >
        <text class="category-icon">{{item.icon}}</text>
        <text class="category-label">{{item.label}}</text>
      </view>
    </view>
  </view>

  <!-- 子分类选择 -->
  <view class="subcategory-section" wx:if="{{category}}">
    <view class="section-title">
      <text>具体类型</text>
    </view>
    <view class="subcategory-container">
      <view 
        wx:for="{{subCategoryOptions[category]}}" 
        wx:key="value" 
        class="subcategory-item {{subCategory === item.value ? 'active' : ''}}"
        bindtap="selectSubCategory"
        data-subcategory="{{item.value}}"
      >
        <view class="subcategory-content">
          <text class="subcategory-label">{{item.label}}</text>
          <text class="subcategory-desc">{{item.desc}}</text>
        </view>
        <view class="subcategory-check" wx:if="{{subCategory === item.value}}">
          <text class="check-icon">✓</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 订单选择 -->
  <view class="order-section" wx:if="{{subCategory === 'order'}}">
    <view class="section-title">
      <text>关联订单</text>
      <text class="optional-text">（可选）</text>
    </view>
    
    <!-- 已选订单 -->
    <view class="selected-order" wx:if="{{selectedOrder && selectedOrder.orderDetails && selectedOrder.orderDetails.length > 0}}">
      <view class="order-content">
        <image class="service-image" src="{{selectedOrder.orderDetails[0].service.logo}}" mode="aspectFill"></image>
        <view class="order-info">
          <text class="service-name">{{selectedOrder.orderDetails[0].service.serviceName}}</text>
          <text class="order-sn">订单号：{{selectedOrder.sn}}</text>
          <text class="order-price">¥{{selectedOrder.totalFee}}</text>
        </view>
        <view class="order-actions">
          <text class="clear-btn" bindtap="clearSelectedOrder">清除</text>
        </view>
      </view>
    </view>
    
    <!-- 选择订单按钮 -->
    <view class="select-order-btn" wx:if="{{!selectedOrder}}" bindtap="loadOrderList">
      <text class="select-text">选择相关订单</text>
      <text class="arrow">></text>
    </view>
  </view>

  <!-- 标题输入 -->
  <view class="title-section">
    <view class="section-title">
      <text>标题</text>
      <text class="required">*</text>
    </view>
    <input 
      class="title-input" 
      placeholder="请输入标题"
      value="{{title}}"
      bindinput="onTitleInput"
      maxlength="{{titleMaxLength}}"
    />
    <view class="char-count">{{title.length}}/{{titleMaxLength}}</view>
  </view>

  <!-- 内容输入 -->
  <view class="content-section">
    <view class="section-title">
      <text>详细描述</text>
      <text class="required">*</text>
    </view>
    <textarea 
      class="content-input" 
      placeholder="{{placeholders[category] && placeholders[category][subCategory] || '请详细描述您的问题或建议'}}"
      value="{{content}}"
      bindinput="onContentInput"
      maxlength="{{contentMaxLength}}"
      auto-height
    ></textarea>
    <view class="char-count">{{content.length}}/{{contentMaxLength}}</view>
  </view>

  <!-- 联系方式 -->
  <view class="contact-section">
    <view class="section-title">
      <text>联系方式</text>
      <text class="required" wx:if="{{category === 'complaint'}}">*</text>
      <text class="optional-text" wx:if="{{category === 'suggestion'}}">（可选）</text>
    </view>
    <input
      class="contact-input {{contactError ? 'error' : ''}}"
      placeholder="{{category === 'complaint' ? '请输入手机号码' : '请输入手机号码（可选）'}}"
      value="{{contactInfo}}"
      bindinput="onContactInput"
      type="number"
    />
    <view class="error-tip" wx:if="{{contactError}}">
      <text class="error-text">{{contactError}}</text>
    </view>
  </view>

  <!-- 图片上传 -->
  <view class="photo-section">
    <view class="section-title">
      <text>上传图片</text>
      <text class="optional-text">（可选）</text>
    </view>
    <view class="photo-container">
      <!-- 已上传的图片 -->
      <view wx:for="{{photoList}}" wx:key="index" class="photo-item">
        <image src="{{item}}" class="photo-preview" mode="aspectFill" bindtap="previewImage" data-url="{{item}}"></image>
        <view class="photo-delete" bindtap="deletePhoto" data-index="{{index}}">
          <text class="delete-icon">×</text>
        </view>
      </view>
      
      <!-- 上传按钮 -->
      <view wx:if="{{photoList.length < 6}}" class="photo-upload" bindtap="chooseImage">
        <view class="upload-icon">
          <text class="upload-text">📷</text>
        </view>
        <text class="upload-label">上传图片</text>
      </view>
    </view>
    <view class="photo-tip">最多可上传6张图片</view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button class="submit-btn {{canSubmit ? 'active' : ''}}" bindtap="submitComplaint" disabled="{{!canSubmit}}">
      {{isEditMode ? '更新' : '提交'}}
    </button>
  </view>
</view>

<!-- 订单选择弹窗 -->
<view class="order-modal" wx:if="{{showOrderSelector}}">
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">选择订单</text>
      <text class="modal-close" bindtap="cancelOrderSelection">×</text>
    </view>
    <scroll-view class="order-list" scroll-y>
      <view
        wx:for="{{orderList}}"
        wx:key="id"
        class="order-item"
        bindtap="selectOrder"
        data-order="{{item}}"
        wx:if="{{item.orderDetails && item.orderDetails.length > 0}}"
      >
        <image class="service-image" src="{{item.orderDetails[0].service.logo}}" mode="aspectFill"></image>
        <view class="order-info">
          <text class="service-name">{{item.orderDetails[0].service.serviceName}}</text>
          <text class="order-sn">订单号：{{item.sn}}</text>
          <text class="order-price">¥{{item.totalFee}}</text>
        </view>
        <text class="select-arrow">></text>
      </view>
      <view wx:if="{{orderList.length === 0}}" class="empty-tip">
        <text>暂无订单</text>
      </view>
    </scroll-view>
  </view>
</view>

<!-- 加载提示 -->
<view class="loading-mask" wx:if="{{isSubmitting}}">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">提交中...</text>
  </view>
</view>
