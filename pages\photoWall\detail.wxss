/* pages/photoWall/detail.wxss */

/* 导航栏样式 */
.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.05);
}

.back-icon {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
}

.nav-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.placeholder {
  width: 60rpx;
}

.photo-detail-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.detail-content {
  padding: 30rpx;
}

/* 照片对比展示 */
.photo-comparison-large {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.before-section,
.after-section {
  margin-bottom: 40rpx;
}

.before-section:last-child,
.after-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.detail-image {
  width: 100%;
  height: 400rpx;
  border-radius: 12rpx;
  background-color: #f0f0f0;
}

/* 照片信息卡片 */
.photo-info-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.photo-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  flex: 1;
  margin-right: 20rpx;
}

.photo-stats {
  display: flex;
  gap: 30rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 10rpx 16rpx;
  border-radius: 20rpx;
  background-color: #f8f8f8;
  transition: all 0.3s ease;
}

.stat-item:active {
  transform: scale(0.95);
}

.icon-heart {
  font-size: 28rpx;
  color: #ccc;
  transition: color 0.3s ease;
}

.icon-heart.liked {
  color: #ff438f;
}

.icon-view {
  font-size: 24rpx;
}

.stat-text {
  font-size: 24rpx;
  color: #666;
}

.info-details {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.detail-row {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
}

.label {
  font-size: 28rpx;
  color: #666;
  min-width: 160rpx;
  flex-shrink: 0;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  line-height: 1.4;
}

.service-type {
  color: #ff438f;
  font-weight: bold;
}

.description {
  line-height: 1.6;
}

/* 订单信息卡片 */
.order-info-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.order-item:active {
  transform: scale(0.98);
  background-color: #f0f0f0;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.order-sn {
  font-size: 28rpx;
  color: #333;
}

.order-status {
  font-size: 24rpx;
  color: #666;
}

.arrow-right {
  font-size: 32rpx;
  color: #ccc;
}

/* 状态样式 */
.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  color: #666;
  font-size: 28rpx;
}

.retry-btn {
  margin-top: 30rpx;
  background-color: #ff438f;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}
