<view class="container photo-detail-container">
  <diy-navbar :isFixed="true" CustomBar='60' bgCustom="#fff">
    <view slot="content">
      <view class="flex align-center flex-nowrap justify-center diygw-col-24">
        照片详情
      </view>
    </view>
  </diy-navbar>

  <view wx:if="{{photoDetail}}" class="detail-content">
    <!-- 照片对比展示 -->
    <view class="photo-comparison-large">
      <view class="before-section">
        <text class="section-title">服务前</text>
        <image 
          src="{{photoDetail.beforePhoto}}" 
          class="detail-image" 
          mode="aspectFit"
          bind:tap="previewImage"
          data-url="{{photoDetail.beforePhoto}}"
        ></image>
      </view>
      
      <view class="after-section">
        <text class="section-title">服务后</text>
        <image 
          src="{{photoDetail.afterPhoto}}" 
          class="detail-image" 
          mode="aspectFit"
          bind:tap="previewImage"
          data-url="{{photoDetail.afterPhoto}}"
        ></image>
      </view>
    </view>

    <!-- 照片信息 -->
    <view class="photo-info-card">
      <view class="info-header">
        <text class="photo-title">{{photoDetail.title || photoDetail.petName + '的' + photoDetail.serviceTypeName}}</text>
        <view class="photo-stats">
          <view class="stat-item" bind:tap="toggleLike">
            <text class="icon-heart {{photoDetail.isLiked ? 'liked' : ''}}">♥</text>
            <text class="stat-text">{{photoDetail.likeCount}}</text>
          </view>
          <view class="stat-item">
            <text class="icon-view">👁</text>
            <text class="stat-text">{{photoDetail.viewCount}}</text>
          </view>
        </view>
      </view>

      <view class="info-details">
        <view class="detail-row">
          <text class="label">宠物信息：</text>
          <text class="value">{{photoDetail.petName}} · {{photoDetail.petType}}</text>
        </view>
        <view class="detail-row">
          <text class="label">服务类型：</text>
          <text class="value service-type">{{photoDetail.serviceTypeName}}</text>
        </view>
        <view wx:if="{{photoDetail.description}}" class="detail-row">
          <text class="label">服务描述：</text>
          <text class="value description">{{photoDetail.description}}</text>
        </view>
        <view class="detail-row">
          <text class="label">服务时间：</text>
          <text class="value">{{formatTime(photoDetail.createdAt)}}</text>
        </view>
      </view>
    </view>

    <!-- 相关订单信息 -->
    <view wx:if="{{photoDetail.orderId}}" class="order-info-card">
      <view class="card-title">相关订单</view>
      <view class="order-item" bind:tap="viewOrderDetail">
        <view class="order-info">
          <text class="order-sn">订单号：{{photoDetail.order.sn || photoDetail.orderId}}</text>
          <text class="order-status">{{photoDetail.order.status || '已完成'}}</text>
        </view>
        <text class="arrow-right">></text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-state">
    <text>加载中...</text>
  </view>

  <!-- 错误状态 -->
  <view wx:if="{{error}}" class="error-state">
    <text>{{error}}</text>
    <button class="retry-btn" bind:tap="loadPhotoDetail">重试</button>
  </view>
</view>
