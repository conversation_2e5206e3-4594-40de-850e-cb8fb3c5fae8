/* pages/service/reservation/index.wxss */
.reservationPage {
  background-color: rgba(246, 246, 246, 1);
  height: 100vh;
  position: relative;
  padding: 40rpx 0 0;
}

.flex-card {
  border: 1px solid rgba(238, 238, 238, 1);
  border-radius: 16rpx;
  padding: 20rpx;
  background-color: white;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  width: calc(100vw - 80rpx);
  margin: 0 40rpx;
}

.diy-icon-min {
  margin-top: 20rpx;
  width: 40rpx !important;
  height: 40rpx !important;
}

.image-size {
  border-radius: 16rpx;
  overflow: hidden;
  height: 160rpx !important;
  width: 160rpx !important;
}

.right-content {
  padding-left: 20rpx;
}

.text-bold {
  font-size: 30rpx;
  line-height: 60rpx;
}

.service-num {
  color: rgba(47, 131, 255, 1);
  font-size: 24rpx;
  background-color: rgba(47, 131, 255, 0.2);
  padding: 8rpx 24rpx;
  border-radius: 80rpx;
  display: inline;
  width: fit-content;
}

.service-navbar .flex-direction-column .diygw-tabs.small-border .diygw-tab-item.cur::after {
  content: "";
  width: 50rpx;
  height: 8rpx;
  position: absolute;
  background-color: rgba(255, 67, 145, 1);
  left: calc(50% - 25rpx);
  bottom: 0;
  margin: auto;
  border-radius: 8rpx;
}

.tab-container {
  background-color: white;
  padding: 20rpx 20rpx 180rpx;
}

.type-group .type-name {
  font-size: 36rpx;
  font-weight: bold;
}

.type-group-children {
  padding: 24rpx 0;
  gap: 24rpx;
}

/* 美化增项服务的选中状态 */
.type-sub-group {
  border: 1px solid rgba(227, 227, 227, 1);
  padding: 20rpx;
  width: 21vw !important;
  display: flex;
  justify-content: space-between;
  text-align: center;
  transition: all 0.3s ease;
}

/* 增项服务选中状态的背景色调整，保持整体视觉和谐 */
.type-sub-group.active {
  /* border: 2rpx solid rgba(255, 67, 145, 0.5); */
  background-color: rgba(255, 67, 145, 0.1);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.type-group-children image {
  width: 72rpx;
  height: 72rpx;
}

.type-sub-name {
  font-size: 24rpx;
  line-height: 56rpx;
}

.type-sub-price {
  font-weight: bold;
}

.type-sub-desc {
  font-size: 20rpx;
  color: #888;
}

.type-group-children .diy-icon-roundadd {
  color: rgba(47, 131, 255, 1);
  font-size: 38rpx;
}

.shop-bar {
  background-color: #fff;
  padding: 24rpx;
  left: 0rpx;
  bottom: 0rpx;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  border-radius: 10rpx 10rpx 0 0;
}

.image3-clz {
  z-index: 1;
  left: 20rpx;
  bottom: 10rpx;
  position: absolute;
}

.image3-size {
  height: 140rpx !important;
  width: 120rpx !important;
}

.text17-clz {
  color: #fff;
  background-color: rgba(47, 131, 255, 1);
  position: absolute !important;
  top: -10rpx;
  left: 150rpx;
  border-radius: 100rpx;
  z-index: 1;
  padding: 8rpx 18rpx;
  font-size: 26rpx !important;
}

.flex12-clz {
  padding-top: 10rpx;
  color: #333;
  flex: 1;
  padding: 10rpx;
}

.color-pink {
  color: rgba(255, 67, 145, 1);
}

.money-num {
  font-size: 32px;
  font-weight: bold;
}

.flex13-clz {
  background-color: rgba(255, 67, 145, 1);
  padding: 10rpx 40rpx;
  color: #fff;
  border-radius: 120rpx;
  height: 70rpx;
  line-height: 50rpx;
  pointer-events: auto !important;
}

.flex-card {
  padding: 20rpx;
  border: 1px solid rgba(238, 238, 238, 1);
  box-shadow: 0rpx 0rpx 10rpx 1px rgba(31, 31, 31, 0.06);
  overflow: hidden;
  width: calc(100% - 20rpx - 20rpx) !important;
  border-radius: 12rpx;
  margin: 20rpx 20rpx 0;
}

.flex-card-title image {
  width: 36rpx;
  height: 36rpx !important;
}

.flex-card-title text {
  font-weight: bold;
  margin-left: 12rpx;
  font-size: 30rpx;
}

.flex46-clz {
  padding: 30rpx;
  border-radius: 24rpx;
  margin: 24rpx 0 0;
  background-color: rgba(255, 192, 218, 0.3);
  overflow: hidden;
}

.flex46-clz image {
  width: 90rpx;
  height: 90rpx !important;
  border: 1px solid #dadada;
  border-radius: 12rpx;
}

.text23-clz {
  padding: 0 32rpx;
  font-weight: bold;
}

.pink-btn {
  border: 1px solid rgba(255, 67, 145, 1);
  background: transparent;
  line-height: 60rpx;
  border-radius: 80rpx;
  font-size: 28rpx;
  margin: 20rpx 0;
}

/* 简化的员工卡片样式 */
.employee-card {
  display: flex;
  align-items: center;
  padding: 24rpx;
  margin: 20rpx 0 0;
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
  border: 1rpx solid #e8eaff;
  border-radius: 16rpx;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(47, 131, 255, 0.08);
}

.employee-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 2rpx solid #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-right: 24rpx;
}

.employee-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.employee-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

.employee-rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.star-icon {
  font-size: 24rpx;
  color: #ffd700;
  margin-right: 4rpx;
}

.rating-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.employee-change-icon {
  width: 32rpx;
  height: 32rpx;
  padding: 8rpx;
  background-color: #2F83FF;
  border-radius: 50%;
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  box-shadow: 0 2rpx 6rpx rgba(47, 131, 255, 0.3);
}

.tagBox {
  color: #2F83FF;
  width: 180rpx;
  line-height: 55rpx;
  border-top: 1px solid #2F83FF;
  border-bottom: 1px solid #2F83FF;
  border-left: 1px solid #2F83FF;
  position: absolute;
  right: -3%;
  border-radius: 90rpx 0 0 90rpx;
}

.tagBox .describe {
  font-weight: 300;
  font-size: 27rpx;
  margin-right: 13rpx;
  margin-left: 0;
}

.tagBox .systemDispatch {
  width: 40rpx;
  height: 40rpx !important;
  margin: 0 10rpx;
}
.flex-address{
  padding: 10rpx;
  overflow: hidden;
  width: 100% !important;
}
.text-adress {
  padding: 10rpx;
  font-size: 28rpx !important;
}
.text-subtip {
  padding: 10rpx;
  color: #7c7c7c;
}
.empty {
  padding: 100rpx 0;
}

.autoHeigth {
  height: 60vh;
  overflow-y: auto;
  box-sizing: border-box;
}

.autoHeigth .serviceIntroduction {
  width: 100%;
}

/* 增项服务列表样式 */
.additional-services-list {
  margin-top: 10rpx;
  padding-left: 20rpx;
  border-left: 2rpx solid #f0f0f0;
}

.text-sm {
  font-size: 24rpx;
}

.text-grey {
  color: #888;
}

.padding-xs {
  padding: 6rpx 0;
}

/* 优惠选择器样式 */
.arrow-right {
  width: 32rpx;
  height: 32rpx;
  margin-left: 10rpx;
}

.color-green {
  color: #07c160;
}

/* 优惠券数量标签 */
.discount-badge {
  display: inline-block;
  background-color: #fff1f6;
  color: #ff4f8f;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-left: 10rpx;
}

/* 增强的优惠选择器行样式 */
.discount-selector-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  margin: 16rpx 0;
  background: linear-gradient(135deg, #fff6f9 0%, #ffffff 100%);
  border: 2rpx solid #ffdbea;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 79, 143, 0.08);
  position: relative;
  transition: all 0.3s ease;
}

.discount-selector-row:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(255, 79, 143, 0.12);
}

.discount-selector-row::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background: linear-gradient(180deg, #ff4f8f 0%, #ff7ba7 100%);
  border-radius: 16rpx 0 0 16rpx;
}

.discount-selector-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.discount-selector-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff4f8f 0%, #ff7ba7 100%);
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 79, 143, 0.3);
}

.coupon-icon-text {
  font-size: 24rpx;
  font-weight: bold;
  color: #ffffff;
  line-height: 1;
}

.discount-selector-content {
  flex: 1;
}

.discount-selector-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
  display: block;
}

.discount-badge-enhanced {
  display: inline-flex;
  align-items: center;
  background: linear-gradient(135deg, #ff4f8f 0%, #ff7ba7 100%);
  color: #ffffff;
  font-size: 22rpx;
  font-weight: 500;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  margin-top: 4rpx;
  box-shadow: 0 2rpx 6rpx rgba(255, 79, 143, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 2rpx 6rpx rgba(255, 79, 143, 0.3);
  }
  50% {
    box-shadow: 0 4rpx 12rpx rgba(255, 79, 143, 0.5);
  }
  100% {
    box-shadow: 0 2rpx 6rpx rgba(255, 79, 143, 0.3);
  }
}

.discount-no-available {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}

.discount-selector-right {
  display: flex;
  align-items: center;
}

.discount-status {
  font-size: 26rpx;
  color: #999;
  margin-right: 8rpx;
}

.discount-status.selected {
  color: #ff4f8f;
  font-weight: 500;
}

.arrow-right-enhanced {
  width: 36rpx;
  height: 36rpx;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.discount-selector-row:active .arrow-right-enhanced {
  opacity: 1;
  transform: translateX(4rpx);
}

.discount-selector-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1001; /* 使用适中的z-index值 */
  display: none;
}

.discount-selector-modal.show {
  display: block;
}

.discount-selector-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.discount-selector-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 80vh;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.discount-selector-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.discount-selector-title {
  font-size: 32rpx;
  font-weight: bold;
}

.discount-selector-close {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.discount-selector-tabs {
  display: flex;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.discount-tab {
  padding: 10rpx 30rpx;
  margin-right: 20rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f5f5f5;
}

.discount-tab.active {
  color: #fff;
  background-color: #ff4f8f;
}

.discount-selector-content {
  flex: 1;
  overflow-y: auto;
  padding: 30rpx;
  max-height: 60vh;
}

.discount-none {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #999;
  font-size: 28rpx;
}

.discount-list {
  display: flex;
  flex-direction: column;
}

.discount-item {
  margin-bottom: 20rpx;
  padding: 20rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #fff6f9 0%, #fff 100%);
  display: flex;
  justify-content: space-between;
  border: 2rpx solid #ffdbea;
  box-shadow: 0 4rpx 12rpx rgba(255, 79, 143, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  transform: translateY(0);
}

.discount-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(255, 79, 143, 0.1);
}

.discount-item::before {
  content: "";
  position: absolute;
  left: -6rpx;
  top: 50%;
  width: 16rpx;
  height: 40rpx;
  background-color: #ff4f8f;
  border-radius: 0 12rpx 12rpx 0;
  transform: translateY(-50%);
}

.discount-item::after {
  content: "";
  position: absolute;
  right: -6rpx;
  top: 50%;
  width: 16rpx;
  height: 40rpx;
  background-color: #ff4f8f;
  border-radius: 12rpx 0 0 12rpx;
  transform: translateY(-50%);
}

.discount-item.selected {
  border-color: #ff4f8f;
  background: linear-gradient(135deg, #fff1f6 0%, #fff6f9 100%);
  box-shadow: 0 6rpx 16rpx rgba(255, 79, 143, 0.2);
}

.discount-item-left {
  flex: 1;
  position: relative;
  z-index: 1;
}

.discount-item-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  color: #333;
}

.discount-item-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  background: rgba(255, 79, 143, 0.05);
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
}

.discount-item-date {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.discount-item-tip {
  font-size: 24rpx;
  color: #ff4f8f;
  margin-top: 8rpx;
  font-weight: 500;
}

.discount-item-right {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  margin-left: 20rpx;
  position: relative;
  z-index: 1;
}

.discount-item-value {
  font-size: 40rpx;
  color: #ff4f8f;
  font-weight: bold;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(255, 79, 143, 0.2);
}

.discount-item-select {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  background-color: #ff4f8f;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 79, 143, 0.3);
  transition: all 0.2s ease;
}

.discount-item:not(.selected) .discount-item-select {
  background-color: #f0f0f0;
  color: transparent;
  border: 2rpx dashed #ccc;
  box-shadow: none;
}

.discount-empty {
  text-align: center;
  padding: 50rpx 0;
  color: #999;
  font-size: 28rpx;
}

.discount-selector-footer {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.discount-selector-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.discount-selector-total-label {
  font-size: 28rpx;
  color: #333;
}

.discount-selector-total-value {
  font-size: 36rpx;
  color: #ff4f8f;
  font-weight: bold;
}

.discount-selector-btn {
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #ff4f8f;
  color: #fff;
  border-radius: 40rpx;
  font-size: 30rpx;
  box-shadow: 0 6rpx 12rpx rgba(255, 79, 143, 0.2);
  transition: all 0.3s ease;
}

.discount-selector-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(255, 79, 143, 0.2);
}

/* 移除了复杂的员工信息样式，使用上面的简化版本 */

/* 用户备注样式 */
.user-remark-input {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fafafa;
  line-height: 1.5;
  box-sizing: border-box;
}

.user-remark-input:focus {
  border-color: #ff4f8f;
  background-color: #fff;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}